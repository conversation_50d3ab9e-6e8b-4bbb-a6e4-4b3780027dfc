"""
ملف التكوين الأساسي لمشروع الطقس والمناخ
Configuration file for Weather and Climate Project
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class Config:
    """كلاس التكوين الأساسي"""
    
    # إعدادات API
    OPENWEATHER_API_KEY = os.getenv('OPENWEATHER_API_KEY', '')
    OPENWEATHER_BASE_URL = "http://api.openweathermap.org/data/2.5"
    
    # الإعدادات الافتراضية
    DEFAULT_CITY = os.getenv('DEFAULT_CITY', 'Doha')
    DEFAULT_LANGUAGE = os.getenv('DEFAULT_LANGUAGE', 'ar')
    DEFAULT_UNITS = os.getenv('DEFAULT_UNITS', 'metric')
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = 'weather_data.db'
    
    # إعدادات الواجهة
    WINDOW_TITLE = "مشروع الطقس والمناخ - Weather & Climate Project"
    WINDOW_SIZE = "800x600"
    
    # الألوان والتصميم
    COLORS = {
        'primary': '#2196F3',
        'secondary': '#FFC107',
        'success': '#4CAF50',
        'danger': '#F44336',
        'warning': '#FF9800',
        'info': '#00BCD4',
        'light': '#F5F5F5',
        'dark': '#212121'
    }
    
    # أيقونات الطقس
    WEATHER_ICONS = {
        'clear': '☀️',
        'clouds': '☁️',
        'rain': '🌧️',
        'snow': '❄️',
        'thunderstorm': '⛈️',
        'drizzle': '🌦️',
        'mist': '🌫️',
        'fog': '🌫️'
    }
    
    # الترجمات
    TRANSLATIONS = {
        'ar': {
            'temperature': 'درجة الحرارة',
            'humidity': 'الرطوبة',
            'pressure': 'الضغط الجوي',
            'wind_speed': 'سرعة الرياح',
            'description': 'الوصف',
            'feels_like': 'تشعر وكأنها',
            'sunrise': 'الشروق',
            'sunset': 'الغروب',
            'search': 'بحث',
            'city_name': 'اسم المدينة',
            'get_weather': 'جلب الطقس',
            'current_weather': 'الطقس الحالي',
            'forecast': 'التوقعات',
            'history': 'السجل',
            'settings': 'الإعدادات'
        },
        'en': {
            'temperature': 'Temperature',
            'humidity': 'Humidity',
            'pressure': 'Pressure',
            'wind_speed': 'Wind Speed',
            'description': 'Description',
            'feels_like': 'Feels Like',
            'sunrise': 'Sunrise',
            'sunset': 'Sunset',
            'search': 'Search',
            'city_name': 'City Name',
            'get_weather': 'Get Weather',
            'current_weather': 'Current Weather',
            'forecast': 'Forecast',
            'history': 'History',
            'settings': 'Settings'
        }
    }
