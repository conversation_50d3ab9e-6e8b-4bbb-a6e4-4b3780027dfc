"""
وحدة جلب بيانات الطقس من OpenWeatherMap API
Weather Data Fetching Module from OpenWeatherMap API
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from config import Config


class WeatherAPI:
    """كلاس للتعامل مع OpenWeatherMap API"""
    
    def __init__(self, api_key: str = None):
        """
        تهيئة كلاس WeatherAPI
        
        Args:
            api_key (str): مفتاح API من OpenWeatherMap
        """
        self.api_key = api_key or Config.OPENWEATHER_API_KEY
        self.base_url = Config.OPENWEATHER_BASE_URL
        
        if not self.api_key:
            raise ValueError("مفتاح API مطلوب - API key is required")
    
    def get_current_weather(self, city: str, lang: str = 'ar', units: str = 'metric') -> Dict:
        """
        جلب بيانات الطقس الحالية لمدينة معينة
        
        Args:
            city (str): اسم المدينة
            lang (str): اللغة (ar للعربية، en للإنجليزية)
            units (str): وحدة القياس (metric, imperial, kelvin)
            
        Returns:
            Dict: بيانات الطقس الحالية
        """
        url = f"{self.base_url}/weather"
        params = {
            'q': city,
            'appid': self.api_key,
            'lang': lang,
            'units': units
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # تنسيق البيانات
            formatted_data = self._format_current_weather(data, units)
            return formatted_data
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"خطأ في جلب البيانات - Error fetching data: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("خطأ في تحليل البيانات - Error parsing data")
    
    def get_forecast(self, city: str, days: int = 5, lang: str = 'ar', units: str = 'metric') -> List[Dict]:
        """
        جلب توقعات الطقس للأيام القادمة
        
        Args:
            city (str): اسم المدينة
            days (int): عدد الأيام (1-5)
            lang (str): اللغة
            units (str): وحدة القياس
            
        Returns:
            List[Dict]: قائمة بتوقعات الطقس
        """
        url = f"{self.base_url}/forecast"
        params = {
            'q': city,
            'appid': self.api_key,
            'lang': lang,
            'units': units
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # تنسيق البيانات
            formatted_data = self._format_forecast_data(data, days, units)
            return formatted_data
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"خطأ في جلب التوقعات - Error fetching forecast: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("خطأ في تحليل بيانات التوقعات - Error parsing forecast data")
    
    def get_weather_by_coordinates(self, lat: float, lon: float, lang: str = 'ar', units: str = 'metric') -> Dict:
        """
        جلب بيانات الطقس باستخدام الإحداثيات الجغرافية
        
        Args:
            lat (float): خط العرض
            lon (float): خط الطول
            lang (str): اللغة
            units (str): وحدة القياس
            
        Returns:
            Dict: بيانات الطقس
        """
        url = f"{self.base_url}/weather"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': self.api_key,
            'lang': lang,
            'units': units
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            formatted_data = self._format_current_weather(data, units)
            return formatted_data
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"خطأ في جلب البيانات بالإحداثيات - Error fetching data by coordinates: {str(e)}")
    
    def _format_current_weather(self, data: Dict, units: str) -> Dict:
        """تنسيق بيانات الطقس الحالية"""
        
        # تحديد وحدات القياس
        temp_unit = '°C' if units == 'metric' else '°F' if units == 'imperial' else 'K'
        speed_unit = 'm/s' if units == 'metric' else 'mph' if units == 'imperial' else 'm/s'
        
        # تحويل أوقات الشروق والغروب
        sunrise = datetime.fromtimestamp(data['sys']['sunrise']).strftime('%H:%M')
        sunset = datetime.fromtimestamp(data['sys']['sunset']).strftime('%H:%M')
        
        # الحصول على أيقونة الطقس
        weather_main = data['weather'][0]['main'].lower()
        icon = Config.WEATHER_ICONS.get(weather_main, '🌤️')
        
        return {
            'city': data['name'],
            'country': data['sys']['country'],
            'temperature': round(data['main']['temp']),
            'feels_like': round(data['main']['feels_like']),
            'humidity': data['main']['humidity'],
            'pressure': data['main']['pressure'],
            'description': data['weather'][0]['description'],
            'main': data['weather'][0]['main'],
            'icon': icon,
            'wind_speed': data['wind']['speed'],
            'wind_direction': data['wind'].get('deg', 0),
            'visibility': data.get('visibility', 0) / 1000,  # تحويل إلى كيلومتر
            'sunrise': sunrise,
            'sunset': sunset,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'units': {
                'temperature': temp_unit,
                'speed': speed_unit,
                'pressure': 'hPa',
                'humidity': '%',
                'visibility': 'km'
            }
        }
    
    def _format_forecast_data(self, data: Dict, days: int, units: str) -> List[Dict]:
        """تنسيق بيانات توقعات الطقس"""
        
        temp_unit = '°C' if units == 'metric' else '°F' if units == 'imperial' else 'K'
        forecast_list = []
        
        # تجميع البيانات حسب اليوم
        daily_data = {}
        
        for item in data['list'][:days * 8]:  # 8 قراءات في اليوم (كل 3 ساعات)
            date = datetime.fromtimestamp(item['dt']).strftime('%Y-%m-%d')
            
            if date not in daily_data:
                daily_data[date] = {
                    'temps': [],
                    'humidity': [],
                    'pressure': [],
                    'descriptions': [],
                    'icons': [],
                    'wind_speeds': []
                }
            
            daily_data[date]['temps'].append(item['main']['temp'])
            daily_data[date]['humidity'].append(item['main']['humidity'])
            daily_data[date]['pressure'].append(item['main']['pressure'])
            daily_data[date]['descriptions'].append(item['weather'][0]['description'])
            daily_data[date]['wind_speeds'].append(item['wind']['speed'])
            
            # أيقونة الطقس
            weather_main = item['weather'][0]['main'].lower()
            icon = Config.WEATHER_ICONS.get(weather_main, '🌤️')
            daily_data[date]['icons'].append(icon)
        
        # تنسيق البيانات النهائية
        for date, day_data in list(daily_data.items())[:days]:
            forecast_list.append({
                'date': date,
                'day_name': datetime.strptime(date, '%Y-%m-%d').strftime('%A'),
                'temp_max': round(max(day_data['temps'])),
                'temp_min': round(min(day_data['temps'])),
                'temp_avg': round(sum(day_data['temps']) / len(day_data['temps'])),
                'humidity_avg': round(sum(day_data['humidity']) / len(day_data['humidity'])),
                'pressure_avg': round(sum(day_data['pressure']) / len(day_data['pressure'])),
                'description': max(set(day_data['descriptions']), key=day_data['descriptions'].count),
                'icon': max(set(day_data['icons']), key=day_data['icons'].count),
                'wind_speed_avg': round(sum(day_data['wind_speeds']) / len(day_data['wind_speeds']), 1),
                'units': {
                    'temperature': temp_unit,
                    'pressure': 'hPa',
                    'humidity': '%',
                    'wind_speed': 'm/s' if units == 'metric' else 'mph'
                }
            })
        
        return forecast_list
    
    def search_cities(self, query: str, limit: int = 5) -> List[Dict]:
        """
        البحث عن المدن
        
        Args:
            query (str): نص البحث
            limit (int): عدد النتائج المطلوبة
            
        Returns:
            List[Dict]: قائمة بالمدن المطابقة
        """
        url = f"http://api.openweathermap.org/geo/1.0/direct"
        params = {
            'q': query,
            'limit': limit,
            'appid': self.api_key
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            cities = []
            for city in data:
                cities.append({
                    'name': city['name'],
                    'country': city['country'],
                    'state': city.get('state', ''),
                    'lat': city['lat'],
                    'lon': city['lon'],
                    'display_name': f"{city['name']}, {city['country']}"
                })
            
            return cities
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"خطأ في البحث عن المدن - Error searching cities: {str(e)}")
    
    def validate_api_key(self) -> bool:
        """التحقق من صحة مفتاح API"""
        try:
            response = requests.get(
                f"{self.base_url}/weather",
                params={'q': 'London', 'appid': self.api_key},
                timeout=5
            )
            return response.status_code == 200
        except:
            return False
