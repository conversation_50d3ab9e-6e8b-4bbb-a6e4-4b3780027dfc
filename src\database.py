"""
وحدة قاعدة البيانات لحفظ سجلات الطقس
Database Module for Weather Records Storage
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from config import Config


class WeatherDatabase:
    """كلاس لإدارة قاعدة بيانات سجلات الطقس"""
    
    def __init__(self, db_path: str = None):
        """
        تهيئة قاعدة البيانات
        
        Args:
            db_path (str): مسار ملف قاعدة البيانات
        """
        self.db_path = db_path or Config.DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # جدول السجلات الحالية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS weather_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    city TEXT NOT NULL,
                    country TEXT,
                    temperature REAL,
                    feels_like REAL,
                    humidity INTEGER,
                    pressure INTEGER,
                    description TEXT,
                    main_weather TEXT,
                    wind_speed REAL,
                    wind_direction INTEGER,
                    visibility REAL,
                    sunrise TEXT,
                    sunset TEXT,
                    timestamp TEXT,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول التوقعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS forecast_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    city TEXT NOT NULL,
                    forecast_date TEXT,
                    temp_max REAL,
                    temp_min REAL,
                    temp_avg REAL,
                    humidity_avg INTEGER,
                    pressure_avg INTEGER,
                    description TEXT,
                    wind_speed_avg REAL,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المدن المفضلة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS favorite_cities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    city_name TEXT UNIQUE NOT NULL,
                    country TEXT,
                    latitude REAL,
                    longitude REAL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول إعدادات المستخدم
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def save_weather_record(self, weather_data: Dict) -> int:
        """
        حفظ سجل طقس جديد
        
        Args:
            weather_data (Dict): بيانات الطقس
            
        Returns:
            int: معرف السجل المحفوظ
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO weather_records (
                    city, country, temperature, feels_like, humidity, pressure,
                    description, main_weather, wind_speed, wind_direction,
                    visibility, sunrise, sunset, timestamp, raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                weather_data.get('city'),
                weather_data.get('country'),
                weather_data.get('temperature'),
                weather_data.get('feels_like'),
                weather_data.get('humidity'),
                weather_data.get('pressure'),
                weather_data.get('description'),
                weather_data.get('main'),
                weather_data.get('wind_speed'),
                weather_data.get('wind_direction'),
                weather_data.get('visibility'),
                weather_data.get('sunrise'),
                weather_data.get('sunset'),
                weather_data.get('timestamp'),
                json.dumps(weather_data, ensure_ascii=False)
            ))
            
            record_id = cursor.lastrowid
            conn.commit()
            return record_id
    
    def save_forecast_records(self, city: str, forecast_data: List[Dict]) -> List[int]:
        """
        حفظ سجلات التوقعات
        
        Args:
            city (str): اسم المدينة
            forecast_data (List[Dict]): بيانات التوقعات
            
        Returns:
            List[int]: قائمة معرفات السجلات المحفوظة
        """
        record_ids = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for forecast in forecast_data:
                cursor.execute('''
                    INSERT INTO forecast_records (
                        city, forecast_date, temp_max, temp_min, temp_avg,
                        humidity_avg, pressure_avg, description, wind_speed_avg, raw_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    city,
                    forecast.get('date'),
                    forecast.get('temp_max'),
                    forecast.get('temp_min'),
                    forecast.get('temp_avg'),
                    forecast.get('humidity_avg'),
                    forecast.get('pressure_avg'),
                    forecast.get('description'),
                    forecast.get('wind_speed_avg'),
                    json.dumps(forecast, ensure_ascii=False)
                ))
                
                record_ids.append(cursor.lastrowid)
            
            conn.commit()
        
        return record_ids
    
    def get_weather_history(self, city: str = None, days: int = 30) -> List[Dict]:
        """
        جلب سجل الطقس التاريخي
        
        Args:
            city (str): اسم المدينة (اختياري)
            days (int): عدد الأيام السابقة
            
        Returns:
            List[Dict]: قائمة سجلات الطقس
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # تحديد تاريخ البداية
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            if city:
                cursor.execute('''
                    SELECT * FROM weather_records 
                    WHERE city = ? AND DATE(created_at) >= ?
                    ORDER BY created_at DESC
                ''', (city, start_date))
            else:
                cursor.execute('''
                    SELECT * FROM weather_records 
                    WHERE DATE(created_at) >= ?
                    ORDER BY created_at DESC
                ''', (start_date,))
            
            records = cursor.fetchall()
            
            # تحويل النتائج إلى قواميس
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, record)) for record in records]
    
    def get_city_statistics(self, city: str, days: int = 30) -> Dict:
        """
        حساب إحصائيات الطقس لمدينة معينة
        
        Args:
            city (str): اسم المدينة
            days (int): عدد الأيام
            
        Returns:
            Dict: إحصائيات الطقس
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            cursor.execute('''
                SELECT 
                    AVG(temperature) as avg_temp,
                    MIN(temperature) as min_temp,
                    MAX(temperature) as max_temp,
                    AVG(humidity) as avg_humidity,
                    AVG(pressure) as avg_pressure,
                    AVG(wind_speed) as avg_wind_speed,
                    COUNT(*) as record_count
                FROM weather_records 
                WHERE city = ? AND DATE(created_at) >= ?
            ''', (city, start_date))
            
            result = cursor.fetchone()
            
            if result and result[6] > 0:  # record_count > 0
                return {
                    'city': city,
                    'period_days': days,
                    'avg_temperature': round(result[0], 1) if result[0] else 0,
                    'min_temperature': round(result[1], 1) if result[1] else 0,
                    'max_temperature': round(result[2], 1) if result[2] else 0,
                    'avg_humidity': round(result[3], 1) if result[3] else 0,
                    'avg_pressure': round(result[4], 1) if result[4] else 0,
                    'avg_wind_speed': round(result[5], 1) if result[5] else 0,
                    'record_count': result[6]
                }
            else:
                return {
                    'city': city,
                    'period_days': days,
                    'record_count': 0,
                    'message': 'لا توجد بيانات كافية لهذه المدينة'
                }
    
    def add_favorite_city(self, city_name: str, country: str = None, lat: float = None, lon: float = None) -> bool:
        """
        إضافة مدينة للمفضلة
        
        Args:
            city_name (str): اسم المدينة
            country (str): اسم الدولة
            lat (float): خط العرض
            lon (float): خط الطول
            
        Returns:
            bool: نجح الحفظ أم لا
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO favorite_cities (city_name, country, latitude, longitude)
                    VALUES (?, ?, ?, ?)
                ''', (city_name, country, lat, lon))
                
                conn.commit()
                return True
        except sqlite3.Error:
            return False
    
    def get_favorite_cities(self) -> List[Dict]:
        """جلب قائمة المدن المفضلة"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM favorite_cities ORDER BY added_at DESC')
            records = cursor.fetchall()
            
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, record)) for record in records]
    
    def remove_favorite_city(self, city_name: str) -> bool:
        """حذف مدينة من المفضلة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('DELETE FROM favorite_cities WHERE city_name = ?', (city_name,))
                conn.commit()
                
                return cursor.rowcount > 0
        except sqlite3.Error:
            return False
    
    def save_user_setting(self, key: str, value: str) -> bool:
        """حفظ إعداد مستخدم"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO user_settings (setting_key, setting_value)
                    VALUES (?, ?)
                ''', (key, value))
                
                conn.commit()
                return True
        except sqlite3.Error:
            return False
    
    def get_user_setting(self, key: str, default_value: str = None) -> str:
        """جلب إعداد مستخدم"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('SELECT setting_value FROM user_settings WHERE setting_key = ?', (key,))
            result = cursor.fetchone()
            
            return result[0] if result else default_value
    
    def cleanup_old_records(self, days: int = 90) -> int:
        """
        حذف السجلات القديمة
        
        Args:
            days (int): عدد الأيام للاحتفاظ بالسجلات
            
        Returns:
            int: عدد السجلات المحذوفة
        """
        cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # حذف سجلات الطقس القديمة
            cursor.execute('DELETE FROM weather_records WHERE DATE(created_at) < ?', (cutoff_date,))
            weather_deleted = cursor.rowcount
            
            # حذف سجلات التوقعات القديمة
            cursor.execute('DELETE FROM forecast_records WHERE DATE(created_at) < ?', (cutoff_date,))
            forecast_deleted = cursor.rowcount
            
            conn.commit()
            
            return weather_deleted + forecast_deleted
    
    def get_database_info(self) -> Dict:
        """جلب معلومات قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # عدد السجلات في كل جدول
            cursor.execute('SELECT COUNT(*) FROM weather_records')
            weather_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM forecast_records')
            forecast_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM favorite_cities')
            favorites_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM user_settings')
            settings_count = cursor.fetchone()[0]
            
            return {
                'database_path': self.db_path,
                'weather_records': weather_count,
                'forecast_records': forecast_count,
                'favorite_cities': favorites_count,
                'user_settings': settings_count,
                'total_records': weather_count + forecast_count
            }
