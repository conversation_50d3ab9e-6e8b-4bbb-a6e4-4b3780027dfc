"""
واجهة المستخدم الرسومية لمشروع الطقس والمناخ
GUI Application for Weather and Climate Project
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
from typing import Dict, List, Optional
import os
import sys

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config
from src.weather_api import WeatherAPI
from src.database import WeatherDatabase


class WeatherGUI:
    """كلاس واجهة المستخدم الرسومية للطقس"""

    def __init__(self):
        """تهيئة الواجهة الرسومية"""
        self.root = tk.Tk()
        self.setup_window()

        # تهيئة المكونات
        self.weather_api = None
        self.database = WeatherDatabase()
        self.current_language = 'ar'
        self.current_units = 'metric'

        # متغيرات الواجهة
        self.city_var = tk.StringVar()
        self.status_var = tk.StringVar(value="جاهز للاستخدام")

        # إنشاء الواجهة
        self.create_widgets()
        self.load_user_settings()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(Config.WINDOW_TITLE)
        self.root.geometry(Config.WINDOW_SIZE)
        self.root.configure(bg=Config.COLORS['light'])

        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # منع تغيير حجم النافذة
        self.root.resizable(True, True)

        # إعداد الشبكة
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # إنشاء شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # إنشاء دفتر الملاحظات (Notebook) للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # إنشاء التبويبات
        self.create_current_weather_tab()
        self.create_forecast_tab()
        self.create_history_tab()
        self.create_settings_tab()

        # شريط الحالة
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.grid_columnconfigure(1, weight=1)

        # مربع البحث
        ttk.Label(toolbar_frame, text="🏙️ المدينة:").grid(row=0, column=0, padx=(0, 5))

        search_frame = ttk.Frame(toolbar_frame)
        search_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        search_frame.grid_columnconfigure(0, weight=1)

        self.city_entry = ttk.Entry(search_frame, textvariable=self.city_var, font=('Arial', 12))
        self.city_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        self.city_entry.bind('<Return>', lambda e: self.search_weather())

        # زر البحث
        self.search_btn = ttk.Button(search_frame, text="🔍 بحث", command=self.search_weather)
        self.search_btn.grid(row=0, column=1)

        # أزرار إضافية
        ttk.Button(toolbar_frame, text="⭐ مفضلة", command=self.show_favorites).grid(row=0, column=2, padx=(10, 5))
        ttk.Button(toolbar_frame, text="🔄 تحديث", command=self.refresh_data).grid(row=0, column=3, padx=5)

    def create_current_weather_tab(self):
        """إنشاء تبويب الطقس الحالي"""
        # إنشاء الإطار
        current_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(current_frame, text="🌤️ الطقس الحالي")

        # تقسيم الإطار إلى قسمين
        current_frame.grid_rowconfigure(0, weight=1)
        current_frame.grid_columnconfigure(0, weight=1)
        current_frame.grid_columnconfigure(1, weight=1)

        # القسم الأيسر - المعلومات الأساسية
        left_frame = ttk.LabelFrame(current_frame, text="المعلومات الأساسية", padding="10")
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # عرض درجة الحرارة الكبيرة
        self.temp_label = ttk.Label(left_frame, text="--°", font=('Arial', 48, 'bold'))
        self.temp_label.grid(row=0, column=0, columnspan=2, pady=10)

        # وصف الطقس
        self.desc_label = ttk.Label(left_frame, text="--", font=('Arial', 14))
        self.desc_label.grid(row=1, column=0, columnspan=2, pady=5)

        # المدينة والدولة
        self.location_label = ttk.Label(left_frame, text="--", font=('Arial', 12, 'bold'))
        self.location_label.grid(row=2, column=0, columnspan=2, pady=5)

        # القسم الأيمن - التفاصيل
        right_frame = ttk.LabelFrame(current_frame, text="التفاصيل", padding="10")
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # إنشاء تسميات التفاصيل
        details = [
            ("الشعور بـ:", "feels_like"),
            ("الرطوبة:", "humidity"),
            ("الضغط الجوي:", "pressure"),
            ("سرعة الرياح:", "wind_speed"),
            ("الرؤية:", "visibility"),
            ("الشروق:", "sunrise"),
            ("الغروب:", "sunset")
        ]

        self.detail_labels = {}
        for i, (label_text, key) in enumerate(details):
            ttk.Label(right_frame, text=label_text).grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(right_frame, text="--")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)

    def create_forecast_tab(self):
        """إنشاء تبويب التوقعات"""
        forecast_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(forecast_frame, text="📅 التوقعات")

        # إطار التوقعات
        forecast_container = ttk.Frame(forecast_frame)
        forecast_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        forecast_frame.grid_rowconfigure(0, weight=1)
        forecast_frame.grid_columnconfigure(0, weight=1)

        # إنشاء إطارات للأيام الخمسة
        self.forecast_frames = []
        for i in range(5):
            day_frame = ttk.LabelFrame(forecast_container, text=f"اليوم {i+1}", padding="5")
            day_frame.grid(row=0, column=i, sticky=(tk.W, tk.E, tk.N, tk.S), padx=2)
            forecast_container.grid_columnconfigure(i, weight=1)

            # عناصر كل يوم
            day_data = {
                'date': ttk.Label(day_frame, text="--", font=('Arial', 10, 'bold')),
                'icon': ttk.Label(day_frame, text="🌤️", font=('Arial', 24)),
                'temp_max': ttk.Label(day_frame, text="--°", font=('Arial', 12, 'bold')),
                'temp_min': ttk.Label(day_frame, text="--°", font=('Arial', 10)),
                'desc': ttk.Label(day_frame, text="--", font=('Arial', 9)),
                'humidity': ttk.Label(day_frame, text="--", font=('Arial', 8))
            }

            # ترتيب العناصر
            day_data['date'].grid(row=0, column=0, pady=2)
            day_data['icon'].grid(row=1, column=0, pady=2)
            day_data['temp_max'].grid(row=2, column=0)
            day_data['temp_min'].grid(row=3, column=0)
            day_data['desc'].grid(row=4, column=0, pady=2)
            day_data['humidity'].grid(row=5, column=0)

            self.forecast_frames.append(day_data)

    def create_history_tab(self):
        """إنشاء تبويب السجل"""
        history_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(history_frame, text="📊 السجل")

        # أدوات التحكم
        control_frame = ttk.Frame(history_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(control_frame, text="عرض سجل:").grid(row=0, column=0, padx=(0, 5))

        self.history_days_var = tk.StringVar(value="30")
        days_combo = ttk.Combobox(control_frame, textvariable=self.history_days_var,
                                 values=["7", "30", "60", "90"], width=10)
        days_combo.grid(row=0, column=1, padx=(0, 5))

        ttk.Label(control_frame, text="يوم").grid(row=0, column=2, padx=(0, 10))

        ttk.Button(control_frame, text="تحديث السجل",
                  command=self.load_history).grid(row=0, column=3)

        # جدول السجل
        history_container = ttk.Frame(history_frame)
        history_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_frame.grid_rowconfigure(1, weight=1)
        history_frame.grid_columnconfigure(0, weight=1)

        # إنشاء Treeview للجدول
        columns = ('التاريخ', 'المدينة', 'درجة الحرارة', 'الوصف', 'الرطوبة')
        self.history_tree = ttk.Treeview(history_container, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120)

        # شريط التمرير
        history_scrollbar = ttk.Scrollbar(history_container, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)

        # ترتيب العناصر
        self.history_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        history_container.grid_rowconfigure(0, weight=1)
        history_container.grid_columnconfigure(0, weight=1)

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")

        # إعدادات API
        api_frame = ttk.LabelFrame(settings_frame, text="إعدادات API", padding="10")
        api_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.grid_columnconfigure(0, weight=1)

        ttk.Label(api_frame, text="مفتاح OpenWeatherMap API:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=50, show="*")
        api_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        api_frame.grid_columnconfigure(0, weight=1)

        ttk.Button(api_frame, text="اختبار المفتاح", command=self.test_api_key).grid(row=1, column=1, padx=(10, 0))

        # إعدادات اللغة والوحدات
        prefs_frame = ttk.LabelFrame(settings_frame, text="التفضيلات", padding="10")
        prefs_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # اللغة
        ttk.Label(prefs_frame, text="اللغة:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.language_var = tk.StringVar(value="ar")
        lang_combo = ttk.Combobox(prefs_frame, textvariable=self.language_var,
                                 values=["ar", "en"], width=15)
        lang_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # وحدات القياس
        ttk.Label(prefs_frame, text="وحدات القياس:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.units_var = tk.StringVar(value="metric")
        units_combo = ttk.Combobox(prefs_frame, textvariable=self.units_var,
                                  values=["metric", "imperial"], width=15)
        units_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # المدينة الافتراضية
        ttk.Label(prefs_frame, text="المدينة الافتراضية:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.default_city_var = tk.StringVar(value="Doha")
        default_city_entry = ttk.Entry(prefs_frame, textvariable=self.default_city_var, width=20)
        default_city_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # أزرار الحفظ
        buttons_frame = ttk.Frame(settings_frame)
        buttons_frame.grid(row=2, column=0, pady=10)

        ttk.Button(buttons_frame, text="حفظ الإعدادات", command=self.save_settings).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(buttons_frame, text="استعادة الافتراضي", command=self.reset_settings).grid(row=0, column=1)

        # معلومات قاعدة البيانات
        db_frame = ttk.LabelFrame(settings_frame, text="قاعدة البيانات", padding="10")
        db_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        self.db_info_text = scrolledtext.ScrolledText(db_frame, height=6, width=60)
        self.db_info_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))

        ttk.Button(db_frame, text="تحديث معلومات قاعدة البيانات",
                  command=self.update_db_info).grid(row=1, column=0, pady=(10, 0))
        ttk.Button(db_frame, text="تنظيف السجلات القديمة",
                  command=self.cleanup_old_records).grid(row=1, column=1, padx=(10, 0), pady=(10, 0))

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.grid_columnconfigure(0, weight=1)

        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # مؤشر التحميل
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.grid(row=0, column=1, sticky=tk.E, padx=(10, 0))

    def search_weather(self):
        """البحث عن الطقس"""
        city = self.city_var.get().strip()
        if not city:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المدينة")
            return

        # تشغيل البحث في خيط منفصل
        threading.Thread(target=self._fetch_weather_data, args=(city,), daemon=True).start()

    def _fetch_weather_data(self, city: str):
        """جلب بيانات الطقس في خيط منفصل"""
        try:
            self.root.after(0, self._start_loading)

            # التأكد من وجود مفتاح API
            if not self.weather_api:
                api_key = self.api_key_var.get() or Config.OPENWEATHER_API_KEY
                if not api_key:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", "مفتاح API مطلوب"))
                    return
                self.weather_api = WeatherAPI(api_key)

            # جلب الطقس الحالي
            current_weather = self.weather_api.get_current_weather(
                city, self.current_language, self.current_units
            )

            # جلب التوقعات
            forecast_data = self.weather_api.get_forecast(
                city, 5, self.current_language, self.current_units
            )

            # حفظ البيانات في قاعدة البيانات
            self.database.save_weather_record(current_weather)
            self.database.save_forecast_records(city, forecast_data)

            # تحديث الواجهة
            self.root.after(0, lambda: self._update_weather_display(current_weather, forecast_data))

        except Exception as e:
            error_msg = f"خطأ في جلب البيانات: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("خطأ", error_msg))
        finally:
            self.root.after(0, self._stop_loading)

    def _start_loading(self):
        """بدء مؤشر التحميل"""
        self.status_var.set("جاري جلب البيانات...")
        self.progress.start(10)
        self.search_btn.configure(state='disabled')

    def _stop_loading(self):
        """إيقاف مؤشر التحميل"""
        self.status_var.set("جاهز للاستخدام")
        self.progress.stop()
        self.search_btn.configure(state='normal')

    def _update_weather_display(self, current_weather: Dict, forecast_data: List[Dict]):
        """تحديث عرض بيانات الطقس"""
        # تحديث الطقس الحالي
        self.temp_label.configure(text=f"{current_weather['temperature']}°")
        self.desc_label.configure(text=f"{current_weather['icon']} {current_weather['description']}")
        self.location_label.configure(text=f"{current_weather['city']}, {current_weather['country']}")

        # تحديث التفاصيل
        self.detail_labels['feels_like'].configure(text=f"{current_weather['feels_like']}°")
        self.detail_labels['humidity'].configure(text=f"{current_weather['humidity']}%")
        self.detail_labels['pressure'].configure(text=f"{current_weather['pressure']} hPa")
        self.detail_labels['wind_speed'].configure(text=f"{current_weather['wind_speed']} m/s")
        self.detail_labels['visibility'].configure(text=f"{current_weather['visibility']} km")
        self.detail_labels['sunrise'].configure(text=current_weather['sunrise'])
        self.detail_labels['sunset'].configure(text=current_weather['sunset'])

        # تحديث التوقعات
        for i, forecast in enumerate(forecast_data[:5]):
            if i < len(self.forecast_frames):
                frame_data = self.forecast_frames[i]
                frame_data['date'].configure(text=forecast['date'])
                frame_data['icon'].configure(text=forecast['icon'])
                frame_data['temp_max'].configure(text=f"{forecast['temp_max']}°")
                frame_data['temp_min'].configure(text=f"{forecast['temp_min']}°")
                frame_data['desc'].configure(text=forecast['description'])
                frame_data['humidity'].configure(text=f"💧 {forecast['humidity_avg']}%")

        self.status_var.set(f"تم تحديث بيانات {current_weather['city']} بنجاح")

    def show_favorites(self):
        """عرض المدن المفضلة"""
        favorites = self.database.get_favorite_cities()

        if not favorites:
            messagebox.showinfo("المفضلة", "لا توجد مدن مفضلة")
            return

        # إنشاء نافذة المفضلة
        fav_window = tk.Toplevel(self.root)
        fav_window.title("المدن المفضلة")
        fav_window.geometry("400x300")

        # قائمة المدن
        listbox = tk.Listbox(fav_window)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for fav in favorites:
            listbox.insert(tk.END, f"{fav['city_name']}, {fav['country']}")

        # أزرار
        btn_frame = ttk.Frame(fav_window)
        btn_frame.pack(pady=10)

        def select_city():
            selection = listbox.curselection()
            if selection:
                city_name = favorites[selection[0]]['city_name']
                self.city_var.set(city_name)
                fav_window.destroy()
                self.search_weather()

        ttk.Button(btn_frame, text="اختيار", command=select_city).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="إغلاق", command=fav_window.destroy).pack(side=tk.LEFT, padx=5)